# Integrated Quantitative Systems Pharmacology Model
# This module integrates disease pathophysiology, pharmacokinetics, and pharmacodynamics
# for pomaglumetad methionil in diabetic neuropathy

library(deSolve)
library(tidyverse)

# Source component models
source("code/models/disease_pathophysiology.R")
source("code/models/pharmacokinetics.R")
source("code/models/pharmacodynamics.R")

#' Integrated QSP Model for Pomaglumetad Methionil in Diabetic Neuropathy
#' 
#' Combines disease progression, drug PK, and PD effects in a unified model
#' 
#' @param time Time vector
#' @param state Combined state variables vector
#' @param parameters Combined model parameters list
#' @return List of derivatives for integrated ODE system

integrated_qsp_model <- function(time, state, parameters) {
  
  # Extract state variables for each component
  n_disease <- 11  # Number of disease state variables
  n_pk <- 9        # Number of PK state variables
  n_pd <- 14       # Number of PD state variables
  
  disease_state <- state[1:n_disease]
  pk_state <- state[(n_disease + 1):(n_disease + n_pk)]
  pd_state <- state[(n_disease + n_pk + 1):(n_disease + n_pk + n_pd)]
  
  names(disease_state) <- c("Glucose", "Glutamate", "ROS", "AGE", "TNF_alpha", 
                           "IL1_beta", "IENFD", "NCV", "Mito_function", "SOD2", "GSH")
  
  names(pk_state) <- c("A_gut", "A_central_prod", "A_periph_prod", "A_central_active",
                      "A_periph_active", "A_CNS_active", "A_DRG_active", 
                      "A_bound_mGluR2", "A_bound_mGluR3")
  
  names(pd_state) <- c("cAMP", "SIRT1", "PGC1a", "TFAM", "Mito_biogenesis",
                      "Glu_transporter", "Gln_synthetase", "SOD2_drug", "GSH_drug",
                      "GFAP", "Body_weight", "Appetite", "Metabolic_rate", "Insulin_sensitivity")
  
  # Calculate drug concentrations and receptor occupancy
  with(parameters, {
    C_DRG_active <- pk_state["A_DRG_active"] / V_DRG
    
    # Calculate receptor occupancy
    occupancy <- calculate_receptor_occupancy(C_DRG_active, parameters)
    total_occupancy <- occupancy$total_occupancy
    
    # Get derivatives from each component model
    disease_derivs <- diabetic_neuropathy_model(time, disease_state, parameters)[[1]]
    pk_derivs <- pomaglumetad_pk_model(time, pk_state, parameters)[[1]]
    pd_derivs <- pomaglumetad_pd_model(time, pd_state, parameters, total_occupancy)[[1]]
    
    # Apply drug effects to disease model
    # Enhance glutamate uptake
    enhanced_uptake_factor <- 1 + (pd_state["Glu_transporter"] - 100) / 100
    disease_derivs[2] <- disease_derivs[2] - 
      (k_glu_uptake * enhanced_uptake_factor - k_glu_uptake) * 
      disease_state["Glutamate"] / (disease_state["Glutamate"] + KM_glu_uptake)
    
    # Enhance antioxidant defense
    enhanced_sod2 <- disease_state["SOD2"] + pd_state["SOD2_drug"]
    enhanced_gsh <- disease_state["GSH"] + pd_state["GSH_drug"]
    
    # Modify ROS scavenging with enhanced antioxidants
    enhanced_ros_scavenging <- k_ros_scav * disease_state["ROS"] * enhanced_sod2 / 
                              (enhanced_sod2 + KM_sod2)
    disease_derivs[3] <- disease_derivs[3] - 
      (enhanced_ros_scavenging - k_ros_scav * disease_state["ROS"] * disease_state["SOD2"] / 
       (disease_state["SOD2"] + KM_sod2))
    
    # Enhance mitochondrial function
    mito_enhancement <- pd_state["Mito_biogenesis"] / 100
    disease_derivs[9] <- disease_derivs[9] + k_mito_drug_enhance * mito_enhancement
    
    # Reduce inflammation (GFAP suppression affects inflammatory cascade)
    inflammation_reduction <- (100 - pd_state["GFAP"]) / 100
    disease_derivs[5] <- disease_derivs[5] * (1 - k_inflammation_suppress * inflammation_reduction)
    disease_derivs[6] <- disease_derivs[6] * (1 - k_inflammation_suppress * inflammation_reduction)
    
    # Apply metabolic effects to glucose (if modeling glucose dynamics)
    # Enhanced insulin sensitivity could improve glucose control
    if (model_glucose_dynamics) {
      insulin_effect <- (pd_state["Insulin_sensitivity"] - 100) / 100
      disease_derivs[1] <- -k_glucose_improve * insulin_effect * 
                          (disease_state["Glucose"] - glucose_target)
    }
    
    # Combine all derivatives
    combined_derivs <- c(disease_derivs, pk_derivs, pd_derivs)
    
    return(list(combined_derivs))
  })
}

#' Get combined parameters for integrated model
get_integrated_parameters <- function() {
  disease_params <- get_default_neuropathy_parameters()
  pk_params <- get_default_pk_parameters()
  pd_params <- get_default_pd_parameters()
  
  # Additional integration parameters
  integration_params <- list(
    # Drug-disease interaction parameters
    k_mito_drug_enhance = 0.1,     # Mitochondrial enhancement factor
    k_inflammation_suppress = 0.3,  # Inflammation suppression factor
    k_glucose_improve = 0.05,       # Glucose improvement rate
    glucose_target = 100,           # Target glucose level (mg/dL)
    model_glucose_dynamics = FALSE  # Whether to model glucose dynamics
  )
  
  # Combine all parameters
  combined_params <- c(disease_params, pk_params, pd_params, integration_params)
  
  return(combined_params)
}

#' Get combined initial conditions
get_integrated_initial_conditions <- function(glucose_level = 200, baseline_weight = 85) {
  disease_ic <- get_healthy_initial_conditions()
  disease_ic["Glucose"] <- glucose_level
  
  pk_ic <- c(
    A_gut = 0,
    A_central_prod = 0,
    A_periph_prod = 0,
    A_central_active = 0,
    A_periph_active = 0,
    A_CNS_active = 0,
    A_DRG_active = 0,
    A_bound_mGluR2 = 0,
    A_bound_mGluR3 = 0
  )
  
  pd_ic <- get_pd_initial_conditions()
  pd_ic["Body_weight"] <- baseline_weight
  
  combined_ic <- c(disease_ic, pk_ic, pd_ic)
  
  return(combined_ic)
}

#' Simulate integrated QSP model with dosing
#' 
#' @param dose Dose amount (mg)
#' @param interval Dosing interval (hours)
#' @param n_doses Number of doses
#' @param glucose_level Chronic glucose level (mg/dL)
#' @param baseline_weight Baseline body weight (kg)
#' @param parameters Model parameters (optional)
#' @return Data frame with integrated simulation results
simulate_integrated_qsp <- function(dose = 40,
                                   interval = 12,
                                   n_doses = 168,  # 4 weeks BID
                                   glucose_level = 200,
                                   baseline_weight = 85,
                                   parameters = NULL) {
  
  if (is.null(parameters)) {
    parameters <- get_integrated_parameters()
  }
  
  # Initial conditions
  initial_state <- get_integrated_initial_conditions(glucose_level, baseline_weight)
  
  # Simulation setup
  all_results <- list()
  current_state <- initial_state
  
  for (i in 1:n_doses) {
    # Add dose to gut compartment
    current_state[12] <- current_state[12] + dose  # A_gut is the 12th state variable
    
    # Simulate for one dosing interval
    times <- seq(0, interval, by = 0.5)
    
    solution <- tryCatch({
      ode(y = current_state,
          times = times,
          func = integrated_qsp_model,
          parms = parameters,
          method = "lsoda")
    }, error = function(e) {
      warning(paste("ODE failed for dose", i, ":", e$message))
      # Create dummy solution if ODE fails
      dummy_solution <- matrix(0, nrow = length(times), ncol = length(current_state) + 1)
      dummy_solution[, 1] <- times
      dummy_solution[, -1] <- rep(current_state, each = length(times))
      return(dummy_solution)
    })
    
    # Store results with consistent column names
    result_df <- as.data.frame(solution)
    
    # Ensure consistent column names for all doses
    expected_cols <- c("time", paste0("V", 1:length(current_state)))
    if (ncol(result_df) == length(expected_cols)) {
      colnames(result_df) <- expected_cols
    }
    
    result_df$time <- result_df$time + (i - 1) * interval
    result_df$dose_number <- i
    result_df$time_days <- result_df$time / 24
    
    all_results[[i]] <- result_df
    
    # Update state for next dose (if solution successful)
    if (nrow(solution) > 1) {
      current_state <- as.numeric(solution[nrow(solution), -1])
    }
  }
  
  # Combine all results with improved error handling
  final_result <- tryCatch({
    # Check that all results have the same column structure
    col_counts <- sapply(all_results, ncol)
    if (length(unique(col_counts)) == 1) {
      # All have same number of columns, safe to rbind
      combined <- do.call(rbind, all_results)
      
      # Add meaningful column names
      state_names <- c("Glucose", "Glutamate", "ROS", "AGE", "TNF_alpha", "IL1_beta", 
                       "IENFD", "NCV", "Mito_function", "SOD2", "GSH",
                       "A_gut", "A_central_prod", "A_periph_prod", "A_central_active",
                       "A_periph_active", "A_CNS_active", "A_DRG_active", 
                       "A_bound_mGluR2", "A_bound_mGluR3",
                       "cAMP", "SIRT1", "PGC1a", "TFAM", "Mito_biogenesis",
                       "Glu_transporter", "Gln_synthetase", "SOD2_drug", "GSH_drug",
                       "GFAP", "Body_weight", "Appetite", "Metabolic_rate", "Insulin_sensitivity")
      
      # Only rename columns if we have the expected number
      if (ncol(combined) >= length(state_names) + 1) {
        colnames(combined)[2:(length(state_names) + 1)] <- state_names
      }
      
      return(combined)
    } else {
      warning("Inconsistent column counts across doses, returning first dose only")
      return(all_results[[1]])
    }
  }, error = function(e) {
    warning(paste("Failed to combine integrated QSP results:", e$message))
    # Return first result only if rbind fails
    return(all_results[[1]])
  })
  
  # Calculate derived variables
  with(parameters, {
    final_result$C_DRG_active <- final_result$A_DRG_active / V_DRG
    
    # Calculate receptor occupancy with error handling
    final_result$receptor_occupancy <- sapply(final_result$C_DRG_active, function(c) {
      tryCatch({
        if (is.na(c) || !is.finite(c) || c < 0) {
          return(0)
        }
        result <- calculate_receptor_occupancy(c, parameters)
        if (is.list(result) && "total_occupancy" %in% names(result)) {
          occ <- result$total_occupancy
          if (is.numeric(occ) && is.finite(occ)) {
            return(occ)
          }
        }
        return(0)
      }, error = function(e) {
        return(0)
      })
    })
    
    # Calculate composite neuropathy score (lower is better)
    final_result$neuropathy_score <- 
      (20 - final_result$IENFD) * 2 +  # IENFD loss (0-40 points)
      (50 - final_result$NCV) * 1 +    # NCV reduction (0-50 points)
      final_result$ROS * 5 +           # Oxidative stress (0-50+ points)
      (final_result$TNF_alpha - 1) * 2 # Inflammation (0-20+ points)
    
    # Weight change from baseline
    final_result$weight_change <- final_result$Body_weight - baseline_weight
    final_result$weight_change_percent <- (final_result$weight_change / baseline_weight) * 100
  })
  
  return(final_result)
}

#' Calculate treatment efficacy endpoints
#' 
#' @param simulation_results Results from simulate_integrated_qsp
#' @param baseline_period Days to use for baseline (default: 7)
#' @param endpoint_period Days to use for endpoint (default: last 7 days)
#' @return List of efficacy endpoints
calculate_efficacy_endpoints <- function(simulation_results, 
                                       baseline_period = 7,
                                       endpoint_period = 7) {
  
  # Check if simulation_results is valid
  if (is.null(simulation_results) || nrow(simulation_results) == 0) {
    warning("Invalid simulation results provided to calculate_efficacy_endpoints")
    return(list(
      neuropathy_score_change = NA,
      weight_change = NA,
      ienfd_change = NA,
      receptor_occupancy_mean = NA
    ))
  }
  
  # Handle missing time_days column
  if (!"time_days" %in% colnames(simulation_results)) {
    if ("time" %in% colnames(simulation_results)) {
      simulation_results$time_days <- simulation_results$time / 24
    } else {
      simulation_results$time_days <- seq(0, nrow(simulation_results) - 1) / 48  # Assume 0.5h intervals
    }
  }
  
  max_time <- max(simulation_results$time_days, na.rm = TRUE)
  
  # Baseline values (first week)
  baseline_data <- simulation_results[simulation_results$time_days <= baseline_period, ]
  if (nrow(baseline_data) == 0) {
    n_points <- min(10, nrow(simulation_results))
    baseline_data <- simulation_results[seq_len(n_points), ]  # First 10 points
  }
  
  # Endpoint values (last week)
  endpoint_data <- simulation_results[simulation_results$time_days >= (max_time - endpoint_period), ]
  if (nrow(endpoint_data) == 0) {
    start_idx <- max(1, nrow(simulation_results) - 9)
    end_idx <- nrow(simulation_results)
    endpoint_data <- simulation_results[start_idx:end_idx, ]  # Last 10 points
  }
  
  # Helper function to safely calculate column means
  safe_col_mean <- function(data, col_name) {
    if (col_name %in% colnames(data)) {
      val <- mean(data[[col_name]], na.rm = TRUE)
      return(if (is.finite(val)) val else NA)
    }
    return(NA)
  }
  
  # Calculate baseline and endpoint values for key variables
  baseline_neuropathy <- safe_col_mean(baseline_data, "neuropathy_score")
  endpoint_neuropathy <- safe_col_mean(endpoint_data, "neuropathy_score")
  
  baseline_weight <- safe_col_mean(baseline_data, "Body_weight")
  endpoint_weight <- safe_col_mean(endpoint_data, "Body_weight")
  
  baseline_ienfd <- safe_col_mean(baseline_data, "IENFD")
  endpoint_ienfd <- safe_col_mean(endpoint_data, "IENFD")
  
  # Calculate receptor occupancy mean - handle both numeric and character data
  receptor_occupancy_mean <- NA
  if ("receptor_occupancy" %in% colnames(endpoint_data)) {
    ro_values <- endpoint_data$receptor_occupancy
    # Ensure values are numeric
    if (is.character(ro_values)) {
      ro_values <- as.numeric(ro_values)
    }
    if (is.numeric(ro_values)) {
      receptor_occupancy_mean <- mean(ro_values, na.rm = TRUE)
      if (!is.finite(receptor_occupancy_mean)) {
        receptor_occupancy_mean <- NA
      }
    }
  }
  
  # Key efficacy endpoints with error handling
  efficacy_endpoints <- list(
    # Primary endpoint
    neuropathy_score_change = if (!is.na(baseline_neuropathy) && !is.na(endpoint_neuropathy)) {
      endpoint_neuropathy - baseline_neuropathy
    } else NA,
    
    # Secondary endpoints
    weight_change = if (!is.na(baseline_weight) && !is.na(endpoint_weight)) {
      endpoint_weight - baseline_weight
    } else NA,
    
    ienfd_change = if (!is.na(baseline_ienfd) && !is.na(endpoint_ienfd)) {
      endpoint_ienfd - baseline_ienfd
    } else NA,
    
    # Biomarker
    receptor_occupancy_mean = receptor_occupancy_mean,
    
    # Additional endpoints for completeness
    baseline_neuropathy_score = baseline_neuropathy,
    endpoint_neuropathy_score = endpoint_neuropathy,
    baseline_weight = baseline_weight,
    endpoint_weight = endpoint_weight,
    baseline_ienfd = baseline_ienfd,
    endpoint_ienfd = endpoint_ienfd,
    
    # Summary statistics
    simulation_duration_days = max_time,
    n_baseline_points = nrow(baseline_data),
    n_endpoint_points = nrow(endpoint_data)
  )
  
  return(efficacy_endpoints)
}

