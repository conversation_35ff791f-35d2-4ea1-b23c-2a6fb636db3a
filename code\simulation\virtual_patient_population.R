# Virtual Patient Population Generation
# This module creates diverse virtual patient populations representing
# Type 1 and Type 2 diabetes patients with varying characteristics

library(tidyverse)
library(dplyr)
library(mvtnorm)
library(truncnorm)

#' Generate Virtual Patient Population
#' 
#' Creates a diverse population of virtual patients with realistic
#' demographic, clinical, and genetic characteristics
#' 
#' @param n_patients Total number of patients to generate
#' @param type1_proportion Proportion of Type 1 diabetes patients (0-1)
#' @param seed Random seed for reproducibility
#' @return Data frame with virtual patient characteristics

generate_virtual_population <- function(n_patients = 1000, 
                                       type1_proportion = 0.15,
                                       seed = 123) {
  
  set.seed(seed)
  
  # Determine diabetes types
  n_type1 <- round(n_patients * type1_proportion)
  n_type2 <- n_patients - n_type1
  
  # Generate Type 1 diabetes patients
  type1_patients <- generate_type1_patients(n_type1)
  
  # Generate Type 2 diabetes patients  
  type2_patients <- generate_type2_patients(n_type2)
  
  # Combine populations
  virtual_population <- rbind(type1_patients, type2_patients)
  
  # Add patient IDs
  virtual_population$patient_id <- paste0("VP_", sprintf("%04d", 1:n_patients))
  
  # Reorder columns
  virtual_population <- virtual_population %>%
    select(patient_id, everything())
  
  return(virtual_population)
}

#' Generate Type 1 Diabetes Patient Population
#' 
#' @param n_patients Number of Type 1 patients to generate
#' @return Data frame with Type 1 patient characteristics
generate_type1_patients <- function(n_patients) {
  
  # Demographics
  age <- rtruncnorm(n_patients, a = 18, b = 75, mean = 35, sd = 12)
  gender <- sample(c("Male", "Female"), n_patients, replace = TRUE, prob = c(0.52, 0.48))
  race <- sample(c("White", "Black", "Hispanic", "Asian", "Other"), 
                n_patients, replace = TRUE, 
                prob = c(0.75, 0.12, 0.08, 0.03, 0.02))
  
  # Disease characteristics
  diabetes_duration <- rtruncnorm(n_patients, a = 1, b = 40, mean = 15, sd = 8)
  age_at_diagnosis <- pmax(1, age - diabetes_duration)
  
  # Glycemic control (Type 1 typically has more variable control)
  hba1c <- rtruncnorm(n_patients, a = 6.5, b = 12, mean = 8.2, sd = 1.3)
  
  # Body weight and BMI (Type 1 patients typically leaner)
  height_cm <- ifelse(gender == "Male", 
                     rnorm(n_patients, 175, 7),
                     rnorm(n_patients, 162, 6))
  
  # BMI distribution for Type 1 (lower than Type 2)
  bmi <- rtruncnorm(n_patients, a = 18, b = 35, mean = 24.5, sd = 3.5)
  weight_kg <- bmi * (height_cm / 100)^2
  
  # Neuropathy characteristics
  neuropathy_severity <- generate_neuropathy_severity(n_patients, diabetes_duration, hba1c)
  
  # Comorbidities (lower prevalence in Type 1)
  hypertension <- rbinom(n_patients, 1, plogis(-1.5 + 0.05 * age + 0.1 * bmi))
  dyslipidemia <- rbinom(n_patients, 1, plogis(-2 + 0.03 * age + 0.2 * hba1c))
  retinopathy <- rbinom(n_patients, 1, plogis(-2.5 + 0.1 * diabetes_duration + 0.3 * hba1c))
  nephropathy <- rbinom(n_patients, 1, plogis(-3 + 0.08 * diabetes_duration + 0.4 * hba1c))
  
  # Medications
  insulin_use <- rep(1, n_patients)  # All Type 1 patients use insulin
  metformin_use <- rbinom(n_patients, 1, 0.1)  # Rare in Type 1
  
  # Genetic factors
  genetic_factors <- generate_genetic_factors(n_patients)
  
  # Laboratory values
  lab_values <- generate_lab_values(n_patients, "Type1", hba1c, weight_kg)
  
  # Create data frame
  type1_df <- data.frame(
    diabetes_type = "Type1",
    age = round(age, 1),
    gender = gender,
    race = race,
    height_cm = round(height_cm, 1),
    weight_kg = round(weight_kg, 1),
    bmi = round(bmi, 1),
    diabetes_duration = round(diabetes_duration, 1),
    age_at_diagnosis = round(age_at_diagnosis, 1),
    hba1c = round(hba1c, 1),
    neuropathy_severity = neuropathy_severity$severity,
    mnsi_score = neuropathy_severity$mnsi_score,
    ienfd = neuropathy_severity$ienfd,
    ncv = neuropathy_severity$ncv,
    hypertension = as.logical(hypertension),
    dyslipidemia = as.logical(dyslipidemia),
    retinopathy = as.logical(retinopathy),
    nephropathy = as.logical(nephropathy),
    insulin_use = as.logical(insulin_use),
    metformin_use = as.logical(metformin_use),
    stringsAsFactors = FALSE
  )
  
  # Add genetic factors and lab values
  type1_df <- cbind(type1_df, genetic_factors, lab_values)
  
  return(type1_df)
}

#' Generate Type 2 Diabetes Patient Population
#' 
#' @param n_patients Number of Type 2 patients to generate
#' @return Data frame with Type 2 patient characteristics
generate_type2_patients <- function(n_patients) {
  
  # Demographics (Type 2 typically older)
  age <- rtruncnorm(n_patients, a = 30, b = 80, mean = 58, sd = 12)
  gender <- sample(c("Male", "Female"), n_patients, replace = TRUE, prob = c(0.55, 0.45))
  race <- sample(c("White", "Black", "Hispanic", "Asian", "Other"), 
                n_patients, replace = TRUE, 
                prob = c(0.65, 0.18, 0.12, 0.04, 0.01))
  
  # Disease characteristics
  diabetes_duration <- rtruncnorm(n_patients, a = 0.5, b = 30, mean = 8, sd = 6)
  age_at_diagnosis <- pmax(18, age - diabetes_duration)
  
  # Glycemic control (Type 2 often better controlled)
  hba1c <- rtruncnorm(n_patients, a = 6.5, b = 11, mean = 7.8, sd = 1.1)
  
  # Body weight and BMI (Type 2 patients typically heavier)
  height_cm <- ifelse(gender == "Male", 
                     rnorm(n_patients, 175, 7),
                     rnorm(n_patients, 162, 6))
  
  # BMI distribution for Type 2 (higher than Type 1)
  bmi <- rtruncnorm(n_patients, a = 25, b = 45, mean = 32, sd = 5)
  weight_kg <- bmi * (height_cm / 100)^2
  
  # Neuropathy characteristics
  neuropathy_severity <- generate_neuropathy_severity(n_patients, diabetes_duration, hba1c)
  
  # Comorbidities (higher prevalence in Type 2)
  hypertension <- rbinom(n_patients, 1, plogis(-0.5 + 0.04 * age + 0.08 * bmi))
  dyslipidemia <- rbinom(n_patients, 1, plogis(-1 + 0.02 * age + 0.15 * hba1c))
  retinopathy <- rbinom(n_patients, 1, plogis(-2 + 0.08 * diabetes_duration + 0.25 * hba1c))
  nephropathy <- rbinom(n_patients, 1, plogis(-2.5 + 0.06 * diabetes_duration + 0.3 * hba1c))
  
  # Medications
  insulin_use <- rbinom(n_patients, 1, plogis(-1 + 0.1 * diabetes_duration + 0.2 * hba1c))
  metformin_use <- rbinom(n_patients, 1, 0.8)  # Common in Type 2
  
  # Genetic factors
  genetic_factors <- generate_genetic_factors(n_patients)
  
  # Laboratory values
  lab_values <- generate_lab_values(n_patients, "Type2", hba1c, weight_kg)
  
  # Create data frame
  type2_df <- data.frame(
    diabetes_type = "Type2",
    age = round(age, 1),
    gender = gender,
    race = race,
    height_cm = round(height_cm, 1),
    weight_kg = round(weight_kg, 1),
    bmi = round(bmi, 1),
    diabetes_duration = round(diabetes_duration, 1),
    age_at_diagnosis = round(age_at_diagnosis, 1),
    hba1c = round(hba1c, 1),
    neuropathy_severity = neuropathy_severity$severity,
    mnsi_score = neuropathy_severity$mnsi_score,
    ienfd = neuropathy_severity$ienfd,
    ncv = neuropathy_severity$ncv,
    hypertension = as.logical(hypertension),
    dyslipidemia = as.logical(dyslipidemia),
    retinopathy = as.logical(retinopathy),
    nephropathy = as.logical(nephropathy),
    insulin_use = as.logical(insulin_use),
    metformin_use = as.logical(metformin_use),
    stringsAsFactors = FALSE
  )
  
  # Add genetic factors and lab values
  type2_df <- cbind(type2_df, genetic_factors, lab_values)
  
  return(type2_df)
}

#' Generate Neuropathy Severity Characteristics
#' 
#' @param n_patients Number of patients
#' @param diabetes_duration Diabetes duration (years)
#' @param hba1c HbA1c levels (%)
#' @return List with neuropathy severity measures
generate_neuropathy_severity <- function(n_patients, diabetes_duration, hba1c) {
  
  # Risk factors for neuropathy severity
  risk_score <- 0.1 * diabetes_duration + 0.2 * (hba1c - 7) + rnorm(n_patients, 0, 0.5)
  
  # MNSI score (0-10, higher is worse)
  mnsi_score <- pmax(0, pmin(10, 2 + risk_score + rnorm(n_patients, 0, 1)))
  
  # Categorize severity based on MNSI
  severity <- ifelse(mnsi_score < 3, "Mild",
                    ifelse(mnsi_score < 6, "Moderate", "Severe"))
  
  # IENFD (intraepidermal nerve fiber density, fibers/mm)
  # Normal: ~20, decreases with severity
  ienfd <- pmax(2, 20 - 2 * mnsi_score + rnorm(n_patients, 0, 2))
  
  # NCV (nerve conduction velocity, m/s)
  # Normal: ~50, decreases with severity
  ncv <- pmax(20, 50 - 3 * mnsi_score + rnorm(n_patients, 0, 3))
  
  return(list(
    severity = severity,
    mnsi_score = round(mnsi_score, 1),
    ienfd = round(ienfd, 1),
    ncv = round(ncv, 1)
  ))
}

#' Generate Genetic Factors
#' 
#' @param n_patients Number of patients
#' @return Data frame with genetic factors
generate_genetic_factors <- function(n_patients) {
  
  # mGluR2/3 receptor expression variants (affects drug response)
  mglur2_expression <- rnorm(n_patients, 1.0, 0.2)  # Relative expression
  mglur3_expression <- rnorm(n_patients, 1.0, 0.25)
  
  # Drug metabolism variants
  cyp_activity <- rtruncnorm(n_patients, a = 0.3, b = 2.0, mean = 1.0, sd = 0.3)
  
  # Antioxidant capacity variants
  sod2_activity <- rtruncnorm(n_patients, a = 0.5, b = 1.8, mean = 1.0, sd = 0.25)
  gsh_capacity <- rtruncnorm(n_patients, a = 0.6, b = 1.6, mean = 1.0, sd = 0.2)
  
  # Inflammatory response variants
  tnf_alpha_baseline <- rtruncnorm(n_patients, a = 0.5, b = 2.0, mean = 1.0, sd = 0.3)
  il1_beta_baseline <- rtruncnorm(n_patients, a = 0.6, b = 1.8, mean = 1.0, sd = 0.25)
  
  return(data.frame(
    mglur2_expression = round(mglur2_expression, 3),
    mglur3_expression = round(mglur3_expression, 3),
    cyp_activity = round(cyp_activity, 3),
    sod2_activity = round(sod2_activity, 3),
    gsh_capacity = round(gsh_capacity, 3),
    tnf_alpha_baseline = round(tnf_alpha_baseline, 3),
    il1_beta_baseline = round(il1_beta_baseline, 3)
  ))
}

#' Generate Laboratory Values
#' 
#' @param n_patients Number of patients
#' @param diabetes_type Type of diabetes
#' @param hba1c HbA1c levels
#' @param weight_kg Body weight
#' @return Data frame with laboratory values
generate_lab_values <- function(n_patients, diabetes_type, hba1c, weight_kg) {
  
  # Fasting glucose (correlated with HbA1c)
  fasting_glucose <- 28.7 * hba1c - 46.7 + rnorm(n_patients, 0, 20)
  fasting_glucose <- pmax(70, fasting_glucose)
  
  # Creatinine (kidney function)
  creatinine <- ifelse(diabetes_type == "Type1",
                      rtruncnorm(n_patients, a = 0.6, b = 2.0, mean = 0.9, sd = 0.2),
                      rtruncnorm(n_patients, a = 0.7, b = 2.5, mean = 1.1, sd = 0.3))
  
  # eGFR (estimated glomerular filtration rate)
  egfr <- pmax(30, 175 * (creatinine^(-1.154)) * (age^(-0.203)) * 
              ifelse(gender == "Female", 0.742, 1))
  
  # Lipid profile
  total_cholesterol <- rtruncnorm(n_patients, a = 120, b = 300, mean = 190, sd = 35)
  hdl_cholesterol <- ifelse(gender == "Male",
                           rtruncnorm(n_patients, a = 25, b = 80, mean = 42, sd = 10),
                           rtruncnorm(n_patients, a = 30, b = 90, mean = 52, sd = 12))
  ldl_cholesterol <- total_cholesterol - hdl_cholesterol - 
                    rtruncnorm(n_patients, a = 50, b = 400, mean = 150, sd = 50)
  
  # Inflammatory markers
  crp <- rtruncnorm(n_patients, a = 0.1, b = 10, mean = 2.5, sd = 2)
  
  return(data.frame(
    fasting_glucose = round(fasting_glucose, 0),
    creatinine = round(creatinine, 2),
    egfr = round(egfr, 0),
    total_cholesterol = round(total_cholesterol, 0),
    hdl_cholesterol = round(hdl_cholesterol, 0),
    ldl_cholesterol = round(ldl_cholesterol, 0),
    crp = round(crp, 1)
  ))
}

#' Stratify Virtual Patient Population
#'
#' Stratifies patients based on key characteristics for clinical trial design
#'
#' @param virtual_population Data frame from generate_virtual_population
#' @return Data frame with stratification variables added
stratify_virtual_population <- function(virtual_population) {

  virtual_population %>%
    mutate(
      # Age stratification
      age_group = case_when(
        age < 40 ~ "Young (18-39)",
        age < 60 ~ "Middle-aged (40-59)",
        TRUE ~ "Older (60+)"
      ),

      # BMI stratification
      bmi_category = case_when(
        bmi < 25 ~ "Normal weight",
        bmi < 30 ~ "Overweight",
        bmi < 35 ~ "Obese Class I",
        TRUE ~ "Obese Class II+"
      ),

      # Neuropathy severity stratification
      neuropathy_stratum = case_when(
        mnsi_score < 3 ~ "Mild neuropathy",
        mnsi_score < 6 ~ "Moderate neuropathy",
        TRUE ~ "Severe neuropathy"
      ),

      # Glycemic control stratification
      glycemic_control = case_when(
        hba1c < 7.5 ~ "Good control",
        hba1c < 9.0 ~ "Moderate control",
        TRUE ~ "Poor control"
      ),

      # Weight loss potential (higher BMI = higher potential)
      weight_loss_potential = case_when(
        bmi >= 35 ~ "High",
        bmi >= 30 ~ "Moderate",
        bmi >= 25 ~ "Low",
        TRUE ~ "Minimal"
      ),

      # Comorbidity burden
      comorbidity_count = as.numeric(hypertension) + as.numeric(dyslipidemia) +
                         as.numeric(retinopathy) + as.numeric(nephropathy),

      comorbidity_burden = case_when(
        comorbidity_count == 0 ~ "No comorbidities",
        comorbidity_count <= 2 ~ "Low burden",
        TRUE ~ "High burden"
      ),

      # Treatment complexity
      treatment_complexity = case_when(
        insulin_use & metformin_use ~ "Complex",
        insulin_use | metformin_use ~ "Moderate",
        TRUE ~ "Simple"
      ),

      # Predicted drug response (based on genetic factors)
      predicted_response = case_when(
        mglur2_expression > 1.2 & mglur3_expression > 1.2 ~ "High responder",
        mglur2_expression > 0.8 & mglur3_expression > 0.8 ~ "Moderate responder",
        TRUE ~ "Low responder"
      )
    )
}

#' Apply Inclusion/Exclusion Criteria
#'
#' Filters virtual population based on clinical trial criteria
#'
#' @param virtual_population Stratified virtual population
#' @param min_age Minimum age (default: 18)
#' @param max_age Maximum age (default: 75)
#' @param min_bmi Minimum BMI (default: 25)
#' @param min_hba1c Minimum HbA1c (default: 6.5)
#' @param max_hba1c Maximum HbA1c (default: 11.0)
#' @param min_mnsi Minimum MNSI score (default: 3)
#' @param exclude_severe_complications Exclude severe complications (default: TRUE)
#' @return Filtered data frame of eligible patients
apply_inclusion_exclusion_criteria <- function(virtual_population,
                                             min_age = 18,
                                             max_age = 75,
                                             min_bmi = 25,
                                             min_hba1c = 6.5,
                                             max_hba1c = 11.0,
                                             min_mnsi = 3,
                                             exclude_severe_complications = TRUE) {

  eligible_population <- virtual_population %>%
    filter(
      # Age criteria
      age >= min_age & age <= max_age,

      # BMI criteria (for weight loss assessment)
      bmi >= min_bmi,

      # Glycemic control criteria
      hba1c >= min_hba1c & hba1c <= max_hba1c,

      # Neuropathy severity criteria
      mnsi_score >= min_mnsi,

      # Kidney function (exclude severe renal impairment)
      egfr >= 30
    )

  # Additional exclusions for severe complications
  if (exclude_severe_complications) {
    eligible_population <- eligible_population %>%
      filter(
        # Exclude patients with very severe neuropathy (IENFD < 3)
        ienfd >= 3,

        # Exclude patients with very poor kidney function
        egfr >= 45,

        # Exclude patients with excessive comorbidity burden
        comorbidity_count <= 3
      )
  }

  # Add eligibility flag
  eligible_population$eligible <- TRUE

  return(eligible_population)
}

#' Generate Randomization Scheme
#'
#' Creates balanced randomization based on stratification factors
#'
#' @param eligible_population Eligible patient population
#' @param treatment_arms Vector of treatment arm names
#' @param stratification_vars Vector of stratification variable names
#' @param block_size Block size for randomization (default: 4)
#' @param seed Random seed (default: 456)
#' @return Data frame with treatment assignments
generate_randomization_scheme <- function(eligible_population,
                                        treatment_arms = c("Placebo", "Pomaglumetad_40mg",
                                                          "Pomaglumetad_80mg", "Active_Control"),
                                        stratification_vars = c("diabetes_type", "neuropathy_stratum",
                                                               "bmi_category"),
                                        block_size = 4,
                                        seed = 456) {

  set.seed(seed)

  # Create stratification combinations
  strata_combinations <- eligible_population %>%
    select(all_of(stratification_vars)) %>%
    distinct() %>%
    mutate(stratum_id = row_number())

  # Assign stratum ID to each patient
  eligible_population <- eligible_population %>%
    left_join(strata_combinations, by = stratification_vars)

  # Generate randomization within each stratum
  randomized_population <- eligible_population %>%
    group_by(stratum_id) %>%
    mutate(
      stratum_size = n(),
      # Create balanced blocks within each stratum
      block_id = ceiling(row_number() / block_size),
      treatment_arm = sample(rep(treatment_arms, length.out = n()))
    ) %>%
    ungroup()

  return(randomized_population)
}

