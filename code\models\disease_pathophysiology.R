# Disease Pathophysiology Module for Diabetic Neuropathy QSP Model
# This module models the complex pathophysiology of diabetic neuropathy

library(deSolve)
library(tidyverse)

#' Diabetic Neuropathy Pathophysiology Model
#' 
#' This function implements the core disease pathophysiology module
#' integrating multiple interconnected pathways:
#' - Glutamatergic dysfunction
#' - Oxidative stress and mitochondrial dysfunction  
#' - Inflammatory cascade
#' - Advanced glycation end products (AGE) formation
#' - Nerve structure and function changes
#'
#' @param time Time vector
#' @param state State variables vector
#' @param parameters Model parameters list
#' @return List of derivatives for ODE system

diabetic_neuropathy_model <- function(time, state, parameters) {
  with(as.list(c(state, parameters)), {
    
    # State variables:
    # Glucose: Blood glucose concentration (mg/dL)
    # Glutamate: Extracellular glutamate in DRG (μM)
    # ROS: Reactive oxygen species level (arbitrary units)
    # AGE: Advanced glycation end products (μg/mL)
    # TNF_alpha: TNF-α concentration (pg/mL)
    # IL1_beta: IL-1β concentration (pg/mL)
    # IENFD: Intraepidermal nerve fiber density (fibers/mm)
    # NCV: Nerve conduction velocity (m/s)
    # Mito_function: Mitochondrial function (% of normal)
    # SOD2: Superoxide dismutase 2 activity (U/mg protein)
    # GSH: Glutathione levels (μM)
    
    # Hyperglycemia effects on glutamate dysfunction
    glucose_effect <- Glucose / (Glucose + KM_glucose)
    glutamate_release <- k_glu_release * glucose_effect
    glutamate_uptake <- k_glu_uptake * Glutamate / (Glutamate + KM_glu_uptake)
    
    # Oxidative stress generation
    ros_production <- k_ros_basal + k_ros_glucose * glucose_effect + 
                     k_ros_glu * Glutamate / (Glutamate + KM_glu_ros)
    ros_scavenging <- k_ros_scav * ROS * SOD2 / (SOD2 + KM_sod2)
    
    # AGE formation and accumulation
    age_formation <- k_age_form * Glucose * (Glucose - glucose_threshold) * 
                    (Glucose > glucose_threshold)
    age_clearance <- k_age_clear * AGE
    
    # Inflammatory cascade
    # TNF-α production stimulated by ROS and AGE
    tnf_production <- k_tnf_basal + k_tnf_ros * ROS + k_tnf_age * AGE
    tnf_clearance <- k_tnf_clear * TNF_alpha
    
    # IL-1β production stimulated by TNF-α and ROS
    il1_production <- k_il1_basal + k_il1_tnf * TNF_alpha + k_il1_ros * ROS
    il1_clearance <- k_il1_clear * IL1_beta
    
    # Mitochondrial dysfunction
    mito_damage <- k_mito_damage * (ROS + k_mito_glu * Glutamate)
    mito_repair <- k_mito_repair * Mito_function
    
    # Antioxidant defense (SOD2) regulation
    sod2_induction <- k_sod2_basal + k_sod2_ros * ROS / (ROS + KM_sod2_ind)
    sod2_degradation <- k_sod2_deg * SOD2
    
    # Glutathione dynamics
    gsh_synthesis <- k_gsh_synth * (1 - GSH / GSH_max)
    gsh_consumption <- k_gsh_cons * ROS * GSH / (GSH + KM_gsh)
    
    # Nerve structure and function damage
    # IENFD loss due to oxidative stress, inflammation, and glutamate toxicity
    ienfd_damage <- k_ienfd_damage * (ROS + TNF_alpha + IL1_beta + 
                                     k_ienfd_glu * Glutamate) * IENFD
    ienfd_repair <- k_ienfd_repair * (IENFD_max - IENFD)
    
    # NCV reduction due to similar factors
    ncv_damage <- k_ncv_damage * (ROS + TNF_alpha + IL1_beta + AGE) * NCV
    ncv_repair <- k_ncv_repair * (NCV_max - NCV)
    
    # Differential equations
    dGlucose <- 0  # Glucose assumed constant for this model
    
    dGlutamate <- glutamate_release - glutamate_uptake - k_glu_decay * Glutamate
    
    dROS <- ros_production - ros_scavenging - k_ros_decay * ROS
    
    dAGE <- age_formation - age_clearance
    
    dTNF_alpha <- tnf_production - tnf_clearance
    
    dIL1_beta <- il1_production - il1_clearance
    
    dIENFD <- -ienfd_damage + ienfd_repair
    
    dNCV <- -ncv_damage + ncv_repair
    
    dMito_function <- -mito_damage + mito_repair
    
    dSOD2 <- sod2_induction - sod2_degradation
    
    dGSH <- gsh_synthesis - gsh_consumption
    
    # Return derivatives
    list(c(dGlucose, dGlutamate, dROS, dAGE, dTNF_alpha, dIL1_beta, 
           dIENFD, dNCV, dMito_function, dSOD2, dGSH))
  })
}

#' Default parameters for diabetic neuropathy model
#' Based on literature values and physiological constraints
get_default_neuropathy_parameters <- function() {
  list(
    # Glucose parameters
    KM_glucose = 180,          # Half-saturation for glucose effects (mg/dL)
    glucose_threshold = 126,   # Threshold for AGE formation (mg/dL)
    
    # Glutamate parameters
    k_glu_release = 0.5,       # Glutamate release rate (μM/h)
    k_glu_uptake = 2.0,        # Maximum glutamate uptake rate (μM/h)
    KM_glu_uptake = 10,        # Half-saturation for glutamate uptake (μM)
    k_glu_decay = 0.1,         # Glutamate decay rate (1/h)
    KM_glu_ros = 20,           # Half-saturation for glutamate-induced ROS (μM)
    
    # ROS parameters
    k_ros_basal = 0.1,         # Basal ROS production (AU/h)
    k_ros_glucose = 0.3,       # Glucose-induced ROS production (AU/h)
    k_ros_glu = 0.2,           # Glutamate-induced ROS production (AU/h)
    k_ros_scav = 1.0,          # ROS scavenging rate (1/h)
    k_ros_decay = 0.05,        # ROS decay rate (1/h)
    KM_sod2 = 50,              # Half-saturation for SOD2 scavenging (U/mg)
    
    # AGE parameters
    k_age_form = 0.001,        # AGE formation rate (μg/mL/h per (mg/dL)²)
    k_age_clear = 0.01,        # AGE clearance rate (1/h)
    
    # Inflammatory parameters
    k_tnf_basal = 0.1,         # Basal TNF-α production (pg/mL/h)
    k_tnf_ros = 0.5,           # ROS-induced TNF-α production (pg/mL/h per AU)
    k_tnf_age = 0.3,           # AGE-induced TNF-α production (pg/mL/h per μg/mL)
    k_tnf_clear = 0.2,         # TNF-α clearance rate (1/h)
    
    k_il1_basal = 0.05,        # Basal IL-1β production (pg/mL/h)
    k_il1_tnf = 0.4,           # TNF-α-induced IL-1β production (pg/mL/h per pg/mL)
    k_il1_ros = 0.3,           # ROS-induced IL-1β production (pg/mL/h per AU)
    k_il1_clear = 0.15,        # IL-1β clearance rate (1/h)
    
    # Mitochondrial parameters
    k_mito_damage = 0.01,      # Mitochondrial damage rate (1/h per AU)
    k_mito_glu = 0.5,          # Glutamate contribution to mito damage
    k_mito_repair = 0.05,      # Mitochondrial repair rate (1/h)
    
    # Antioxidant parameters
    k_sod2_basal = 2.0,        # Basal SOD2 production (U/mg/h)
    k_sod2_ros = 5.0,          # ROS-induced SOD2 production (U/mg/h)
    KM_sod2_ind = 10,          # Half-saturation for SOD2 induction (AU)
    k_sod2_deg = 0.1,          # SOD2 degradation rate (1/h)
    
    # Glutathione parameters
    k_gsh_synth = 10,          # GSH synthesis rate (μM/h)
    GSH_max = 1000,            # Maximum GSH concentration (μM)
    k_gsh_cons = 0.5,          # GSH consumption rate (1/h per AU)
    KM_gsh = 100,              # Half-saturation for GSH consumption (μM)
    
    # Nerve damage parameters
    k_ienfd_damage = 0.001,    # IENFD damage rate (1/h per combined factors)
    k_ienfd_glu = 0.5,         # Glutamate contribution to IENFD damage
    k_ienfd_repair = 0.01,     # IENFD repair rate (1/h)
    IENFD_max = 20,            # Maximum IENFD (fibers/mm)
    
    k_ncv_damage = 0.0005,     # NCV damage rate (1/h per combined factors)
    k_ncv_repair = 0.005,      # NCV repair rate (1/h)
    NCV_max = 50               # Maximum NCV (m/s)
  )
}

#' Initial conditions for healthy state
get_healthy_initial_conditions <- function() {
  c(
    Glucose = 90,              # Normal fasting glucose (mg/dL)
    Glutamate = 5,             # Normal glutamate level (μM)
    ROS = 1,                   # Basal ROS level (AU)
    AGE = 0.1,                 # Minimal AGE level (μg/mL)
    TNF_alpha = 1,             # Basal TNF-α (pg/mL)
    IL1_beta = 0.5,            # Basal IL-1β (pg/mL)
    IENFD = 20,                # Normal IENFD (fibers/mm)
    NCV = 50,                  # Normal NCV (m/s)
    Mito_function = 100,       # Normal mitochondrial function (%)
    SOD2 = 100,                # Normal SOD2 activity (U/mg)
    GSH = 800                  # Normal GSH level (μM)
  )
}

#' Simulate diabetic neuropathy progression
#' 
#' @param glucose_level Chronic glucose level (mg/dL)
#' @param duration Simulation duration (hours)
#' @param parameters Model parameters (optional)
#' @return Data frame with simulation results
simulate_neuropathy_progression <- function(glucose_level = 200, 
                                          duration = 8760,  # 1 year in hours
                                          parameters = NULL) {
  
  if (is.null(parameters)) {
    parameters <- get_default_neuropathy_parameters()
  }
  
  # Set initial conditions
  initial_state <- get_healthy_initial_conditions()
  initial_state["Glucose"] <- glucose_level
  
  # Time points
  times <- seq(0, duration, by = 24)  # Daily time points
  
  # Solve ODE system
  solution <- ode(y = initial_state, 
                  times = times, 
                  func = diabetic_neuropathy_model, 
                  parms = parameters,
                  method = "lsoda")
  
  # Convert to data frame
  result <- as.data.frame(solution)
  result$time_days <- result$time / 24
  
  return(result)
}



